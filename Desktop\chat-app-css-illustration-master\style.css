/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Rubik', sans-serif;
  font-size: 16px;
  line-height: 1.5;
  background-color: hsl(270, 20%, 96%);
  min-height: 100vh;
  overflow-x: hidden;
}

/* Container and layout */
.container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
  position: relative;
  max-width: 1440px;
  margin: 0 auto;
}

/* Background gradients */
.bg-gradient {
  position: absolute;
  width: 510px;
  height: 1018px;
  border-radius: 0 0 255px 255px;
  background: linear-gradient(225deg, hsl(293, 100%, 63%) 0%, hsl(264, 100%, 61%) 100%);
  z-index: -1;
}

.bg-gradient--left {
  top: 0;
  left: -255px;
}

.bg-gradient--right {
  bottom: 0;
  right: -255px;
  background: linear-gradient(45deg, hsl(293, 100%, 63%) 0%, hsl(264, 100%, 61%) 100%);
  opacity: 0.05;
}

/* Phone section */
.phone-section {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.phone {
  width: 247px;
  height: 505px;
  background: white;
  border-radius: 30px;
  padding: 11px;
  box-shadow: 0 30px 60px -10px rgba(62, 39, 83, 0.25);
  position: relative;
}

.phone__notch {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 129px;
  height: 29px;
  background: white;
  border-radius: 0 0 14px 14px;
  z-index: 2;
}

/* Chat header */
.chat-header {
  background: linear-gradient(225deg, hsl(264, 100%, 61%) 0%, hsl(293, 100%, 63%) 100%);
  border-radius: 20px 20px 6px 6px;
  padding: 30px 16px 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: white;
}

.chat-header__left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.back-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
}

.avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 1px solid white;
}

.contact-info {
  margin-left: 8px;
}

.contact-name {
  font-size: 11px;
  font-weight: 500;
  margin-bottom: 2px;
}

.contact-status {
  font-size: 8px;
  color: hsl(276, 100%, 81%);
}

.menu-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
}

/* Chat messages */
.chat-messages {
  padding: 11px 8px;
  height: 340px;
  overflow-y: auto;
}

.message {
  margin-bottom: 8px;
  display: flex;
}

.message--received {
  justify-content: flex-start;
}

.message--sent {
  justify-content: flex-end;
}

.message p {
  max-width: 128px;
  padding: 6px 8px;
  border-radius: 10px;
  font-size: 8px;
  line-height: 1.4;
}

.message--received p {
  background: hsl(270, 20%, 96%);
  color: hsl(276, 55%, 52%);
  border-bottom-left-radius: 4px;
}

.message--sent p {
  background: white;
  color: hsl(271, 15%, 43%);
  border-bottom-right-radius: 4px;
  box-shadow: 0 10px 5px -5px rgba(62, 39, 83, 0.05);
}

/* Dog images */
.dog-images {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.dog-images img {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  object-fit: cover;
}

/* Pricing messages */
.message--pricing {
  margin-bottom: 8px;
}

.message--pricing .pricing-option {
  background: linear-gradient(225deg, hsl(293, 100%, 63%) 0%, hsl(264, 100%, 61%) 100%);
  color: white;
  padding: 10px 8px;
  border-radius: 10px 10px 4px 10px;
  display: flex;
  align-items: center;
  gap: 8px;
  max-width: 160px;
  font-size: 8px;
}

.radio-btn {
  width: 12px;
  height: 12px;
  border: 1px solid hsl(289, 100%, 72%);
  border-radius: 50%;
  flex-shrink: 0;
}

.pricing-text {
  flex: 1;
}

.pricing-amount {
  font-weight: 700;
  font-size: 12px;
}

/* Message input */
.message-input {
  position: relative;
  margin: 0 8px;
}

.message-input input {
  width: 100%;
  padding: 13px 50px 13px 20px;
  border: none;
  border-radius: 17px;
  font-size: 9px;
  color: hsl(271, 15%, 43%);
  background: white;
}

.message-input input::placeholder {
  color: hsl(206, 6%, 79%);
}

.send-btn {
  position: absolute;
  right: 4px;
  top: 50%;
  transform: translateY(-50%);
  width: 25px;
  height: 25px;
  border-radius: 50%;
  border: none;
  background: hsl(271, 36%, 24%);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.send-btn:hover {
  background: hsl(271, 36%, 20%);
}

.send-btn svg {
  width: 12px;
  height: 12px;
}

/* Content section */
.content-section {
  flex: 1;
  max-width: 445px;
  margin-left: 125px;
}

.main-heading {
  font-size: 40px;
  font-weight: 500;
  color: hsl(271, 36%, 24%);
  margin-bottom: 24px;
}

.main-description {
  color: hsl(270, 7%, 64%);
  line-height: 1.7;
}

/* Attribution */
.attribution {
  position: fixed;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
  font-size: 11px;
  text-align: center;
  color: hsl(270, 7%, 64%);
}

.attribution a {
  color: hsl(228, 45%, 44%);
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    flex-direction: column;
    gap: 64px;
    padding: 64px 1rem 2rem;
  }

  .bg-gradient--left {
    width: 375px;
    height: 640px;
    left: -187px;
  }

  .bg-gradient--right {
    width: 375px;
    height: 640px;
    right: -187px;
  }

  .content-section {
    margin-left: 0;
    text-align: center;
    max-width: 375px;
  }

  .main-heading {
    font-size: 32px;
  }
}
