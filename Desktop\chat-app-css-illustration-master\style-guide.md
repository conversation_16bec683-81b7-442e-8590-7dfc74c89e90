# Front-end Style Guide

## Layout

The designs were created to the following widths:

- Mobile: 375px
- Desktop: 1440px

> 💡 These are just the design sizes. Ensure content is responsive and meets WCAG requirements by testing the full range of screen sizes from 320px to large screens.

## Colors

### Primary

#### Text

- Pale Violet (sub-heading at the top of the app UI): hsl(276, 100%, 81%)
- Moderate Violet (chat on the left): hsl(276, 55%, 52%)
- Desaturated Dark Violet (chat on the right): hsl(271, 15%, 43%)
- Grayish Blue (placeholder text): hsl(206, 6%, 79%)
- Very Dark Desaturated Violet (main heading): hsl(271, 36%, 24%)
- Dark Grayish Violet (paragraph): hsl(270, 7%, 64%)

#### Gradients

These two colors are the vibrant background colors you see throughout the design and are applied as a linear gradient:

- Light Magenta: hsl(293, 100%, 63%)
- Light Violet: hsl(264, 100%, 61%)

### Secondary

- White: hsl(0, 0%, 100%)
- Light Grayish Violet (app background): hsl(270, 20%, 96%)
- Very Dark Desaturated Violet (submit button background): hsl(271, 36%, 24%)
- Very Light Magenta (radio button outline): hsl(289, 100%, 72%)

## Typography

### Body Copy

- Font size: 16px

### Font

- Family: [Rubik](https://fonts.google.com/specimen/Rubik)
- Weights: 400, 500, 700

> 💎 [Upgrade to Pro](https://www.frontendmentor.io/pro?ref=style-guide) for design file access to see all design details and get hands-on experience using a professional workflow with tools like Figma.
