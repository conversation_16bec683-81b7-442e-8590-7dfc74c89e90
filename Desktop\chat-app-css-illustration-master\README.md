# Frontend Mentor - Chat App CSS Illustration Solution

This is a solution to the [Chat app CSS illustration challenge on Frontend Mentor](https://www.frontendmentor.io/challenges/chat-app-css-illustration-O5auMkFqY). Frontend Mentor challenges help you improve your coding skills by building realistic projects.

## Table of contents

- [Overview](#overview)
  - [The challenge](#the-challenge)
  - [Screenshot](#screenshot)
- [My process](#my-process)
  - [Built with](#built-with)
  - [What I learned](#what-i-learned)
  - [Key features](#key-features)
  - [Continued development](#continued-development)
- [Author](#author)

## Overview

### The challenge

Users should be able to:

- ✅ View the optimal layout for the component depending on their device's screen size
- ✅ See a realistic chat interface with proper message styling
- ✅ View pricing options with interactive-looking radio buttons
- ✅ Experience a responsive design that works on mobile and desktop

### Screenshot

![Design preview for the Chat app CSS illustration coding challenge](./design/desktop-preview.jpg)


## My process

### Built with

- ✨ **Semantic HTML5 markup** - Proper structure with sections, headers, and ARIA labels
- 🎨 **CSS custom properties** - Using HSL color values from the style guide
- 📱 **Flexbox** - For layout and alignment
- 📐 **Mobile-first workflow** - Responsive design starting from 375px
- 🔤 **Google Fonts** - Rubik font family with multiple weights
- 🎯 **CSS Gradients** - Linear gradients for backgrounds and chat header
- 🖼️ **SVG Icons** - Custom SVG icons for buttons and interface elements

### What I learned

This project was an excellent exercise in creating realistic UI components with pure CSS. Here are some key learnings:

**1. Creating a realistic phone mockup:**
```css
.phone {
  width: 247px;
  height: 505px;
  background: white;
  border-radius: 30px;
  padding: 11px;
  box-shadow: 0 30px 60px -10px rgba(62, 39, 83, 0.25);
}
```

**2. Implementing chat bubble styling:**
```css
.message--received p {
  background: hsl(270, 20%, 96%);
  color: hsl(276, 55%, 52%);
  border-bottom-left-radius: 4px;
}

.message--sent p {
  background: white;
  color: hsl(271, 15%, 43%);
  border-bottom-right-radius: 4px;
  box-shadow: 0 10px 5px -5px rgba(62, 39, 83, 0.05);
}
```

**3. Creating gradient pricing cards:**
```css
.message--pricing .pricing-option {
  background: linear-gradient(225deg, hsl(293, 100%, 63%) 0%, hsl(264, 100%, 61%) 100%);
  color: white;
  padding: 10px 8px;
  border-radius: 10px 10px 4px 10px;
}
```

### Key features

- 📱 **Responsive Design**: Works perfectly on mobile (375px) and desktop (1440px)
- 🎨 **Pixel-Perfect Styling**: Matches the design specifications exactly
- 💬 **Realistic Chat Interface**: Proper message alignment and bubble styling
- 🐕 **Image Integration**: Dog photos displayed in chat messages
- 💰 **Interactive Pricing**: Styled pricing options with radio buttons
- ✨ **Smooth Interactions**: Hover effects on interactive elements
- 🎯 **Accessibility**: ARIA labels and semantic HTML structure

### Continued development

Future enhancements could include:

- 🎬 **CSS Animations**: Add message typing animations and slide-in effects
- ⚡ **JavaScript Interactivity**: Make the chat interface functional
- 🎨 **Theme Switching**: Add dark/light mode toggle
- 📱 **Progressive Web App**: Add PWA capabilities for mobile installation


---

## 🚀 Quick Start

1. Clone this repository
2. Open `index.html` in your browser
3. Enjoy the chat app illustration!

## 📁 Project Structure

```
chat-app-css-illustration/
├── index.html          # Main HTML file
├── style.css           # All CSS styles
├── images/             # Avatar and dog images
├── design/             # Design reference files
└── README.md           # This file
```

## Author

- Frontend Mentor - [@Ayokanmi-Adejola](https://www.frontendmentor.io/profile/Ayokanmi-Adejola)
